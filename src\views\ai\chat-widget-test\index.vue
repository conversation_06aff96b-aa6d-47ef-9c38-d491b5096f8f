<template>
  <div class="chat-widget-test">
    <ContentWrap title="AI聊天组件测试">
      <div class="test-container">
        <div class="test-section">
          <h3>基础功能测试</h3>
          <div class="widget-container">
            <AiChatWidget 
              height="400px"
              width="100%"
              title="测试助手"
              welcome-message="这是一个测试页面，用于验证AI聊天组件的功能。"
              :suggestions="testSuggestions"
              placeholder="输入测试消息..."
              @message-sent="onMessageSent"
              @message-received="onMessageReceived"
              @error="onError"
              @stream-start="onStreamStart"
              @stream-end="onStreamEnd"
            />
          </div>
        </div>

        <div class="test-section">
          <h3>测试日志</h3>
          <div class="log-container">
            <div 
              v-for="(log, index) in logs" 
              :key="index"
              class="log-item"
              :class="log.type"
            >
              <span class="log-time">{{ log.time }}</span>
              <span class="log-type">{{ log.type.toUpperCase() }}</span>
              <span class="log-message">{{ log.message }}</span>
            </div>
          </div>
          <div class="log-actions">
            <el-button @click="clearLogs" size="small">清空日志</el-button>
            <el-button @click="testConnection" size="small" type="primary">测试连接</el-button>
          </div>
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'AiChatWidgetTest' })

// 测试建议
const testSuggestions = ref([
  '测试基础对话功能',
  '测试流式生成',
  '测试错误处理',
  '你好，请介绍一下自己'
])

// 日志记录
const logs = ref<Array<{
  time: string
  type: 'info' | 'success' | 'warning' | 'error'
  message: string
}>>([])

// 添加日志
const addLog = (type: 'info' | 'success' | 'warning' | 'error', message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, type, message })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 事件处理函数
const onMessageSent = (message: string) => {
  addLog('info', `用户发送消息: ${message}`)
  ElMessage.info('消息已发送')
}

const onMessageReceived = (message: string) => {
  addLog('success', `收到AI回复: ${message.substring(0, 50)}${message.length > 50 ? '...' : ''}`)
  ElMessage.success('收到AI回复')
}

const onError = (error: Error) => {
  addLog('error', `发生错误: ${error.message}`)
  ElMessage.error(`错误: ${error.message}`)
}

const onStreamStart = () => {
  addLog('info', '开始流式生成')
}

const onStreamEnd = () => {
  addLog('success', '流式生成结束')
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('info', '日志已清空')
}

// 测试连接
const testConnection = () => {
  addLog('info', '开始测试API连接...')
  
  // 这里可以添加实际的连接测试逻辑
  setTimeout(() => {
    addLog('success', 'API连接测试完成')
    ElMessage.success('连接测试完成')
  }, 1000)
}

// 初始化日志
addLog('info', '组件测试页面已加载')
</script>

<style lang="scss" scoped>
.chat-widget-test {
  .test-container {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .test-section {
    h3 {
      margin: 0 0 16px 0;
      color: #1f2937;
      font-size: 18px;
      font-weight: 600;
    }

    .widget-container {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      background: #f9fafb;
    }

    .log-container {
      max-height: 300px;
      overflow-y: auto;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background: #ffffff;
      margin-bottom: 12px;

      .log-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-bottom: 1px solid #f3f4f6;
        font-size: 13px;

        &:last-child {
          border-bottom: none;
        }

        &.info {
          background: #f0f9ff;
          border-left: 3px solid #0ea5e9;
        }

        &.success {
          background: #f0fdf4;
          border-left: 3px solid #22c55e;
        }

        &.warning {
          background: #fffbeb;
          border-left: 3px solid #f59e0b;
        }

        &.error {
          background: #fef2f2;
          border-left: 3px solid #ef4444;
        }

        .log-time {
          color: #6b7280;
          font-family: monospace;
          min-width: 80px;
        }

        .log-type {
          color: #1f2937;
          font-weight: 600;
          min-width: 60px;
        }

        .log-message {
          color: #4b5563;
          flex: 1;
          word-break: break-all;
        }
      }
    }

    .log-actions {
      display: flex;
      gap: 8px;
    }
  }
}
</style>
