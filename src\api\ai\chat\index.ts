import request from '@/config/axios'
import config from '@/config/env'

// API方法
export const apiService = {
  // 获取热点新闻
  getHotspotNews: async (params: {
    pageNo?: number
    pageSize?: number
    sceneType?: number
    messageId?: string
    topFive?: number
    type?: string // 新闻类型筛选
  }): Promise<any> => {
    try {
      const apiPrefix = config.env === 'production' ? '' : '/talent'
      const response = await request.get({
        url: `${apiPrefix}/app-api/system/hotspot/page`,
        params
      })

      if (response.data.code === 0) {
        return response.data.data
      } else {
        throw new Error(response.data.msg || '获取热点新闻失败')
      }
    } catch (error) {
      console.error('获取热点新闻失败:', error)
      return { list: [], total: 0 }
    }
  },

  // 获取聊天历史 - 使用AI API客户端
  getChatHistory: async (
    assistantType: string = 'official',
    page: number = 1,
    pageSize: number = 10
  ): Promise<ChatHistoryItem[]> => {
    console.log(
      `API: getChatHistory 开始调用, 参数: assistantType=${assistantType}, page=${page}, pageSize=${pageSize}`
    )
    try {
      const url = `/ai/chat_history?assistantType=${assistantType}&page=${page}&pageSize=${pageSize}`
      console.log(`API: 请求URL: ${url}`)

      const response = await request.get({ url })
      console.log(`API: getChatHistory 响应状态码: ${response.data.code}`)

      if (response.data.code === 200) {
        const resultList = response.data.data.list || []
        console.log(`API: getChatHistory 成功, 返回 ${resultList.length} 条记录`)
        if (resultList.length > 0) {
          console.log('API: 第一条记录:', {
            id: resultList[0].conversationId,
            title: resultList[0].title,
            updateTime: resultList[0].updateTime
          })
        }
        return resultList
      } else {
        console.error(`API: getChatHistory 失败, 错误信息: ${response.data.message}`)
        throw new Error(response.data.message || '获取聊天历史失败')
      }
    } catch (error) {
      console.error('API: 获取聊天历史出错:', error)
      return []
    }
  },

  // 获取聊天详情 - 使用AI API客户端
  getChatDetail: async (conversationId: string): Promise<ChatDetailItem[]> => {
    try {
      const response = await request.get({
        url: `/ai/chat_detail?conversationId=${conversationId}&assistantType=official`
      })
      if (response.data.code === 200) {
        console.log('获取会话详情成功:', response.data)
        return response.data.data.list
      } else {
        throw new Error(response.data.message || '获取聊天详情失败')
      }
    } catch (error) {
      console.error('获取聊天详情出错:', error)
      return []
    }
  },

  // 用户登录
  login: async (username: string, password: string): Promise<LoginResponse['data'] | null> => {
    try {
      const apiPrefix = config.env === 'production' ? '' : '/talent'
      const response = await request.post({
        url: `${apiPrefix}/admin-api/system/auth/login`,
        data: {
          username,
          password
        }
      })

      if (response.data.code === 0) {
        // 登录成功，保存token信息
        const { accessToken, refreshToken, userId } = response.data.data
        localStorage.setItem('accessToken', accessToken)
        localStorage.setItem('refreshToken', refreshToken)
        localStorage.setItem('userId', userId.toString())
        return response.data.data
      } else {
        throw new Error(response.data.msg || '登录失败')
      }
    } catch (error) {
      console.error('登录失败:', error)
      return null
    }
  },

  // 用户退出
  logout: async (): Promise<boolean> => {
    try {
      const apiPrefix = config.env === 'production' ? '' : '/talent'
      const response = await request.post({ url: `${apiPrefix}/app-api/member/auth/logout` })

      if (response.data.code === 0) {
        console.log('[退出登录] 开始清理所有数据缓存')

        // 退出成功，清除本地存储的token信息
        localStorage.removeItem('accessToken')
        localStorage.removeItem('refreshToken')
        localStorage.removeItem('userId')
        localStorage.removeItem('cerebro-conversations')

        // 清除微信登录相关的会话存储
        sessionStorage.removeItem('wx_processed_auth_codes')

        // 清除与聊天相关的所有本地存储数据
        localStorage.removeItem('currentConversationId')
        localStorage.removeItem('chatHistory')
        localStorage.removeItem('hasSentMessage')

        // 清除当前会话的所有状态
        sessionStorage.removeItem('currentSession')
        sessionStorage.removeItem('lastMessage')

        // 清除可能存在的聊天状态
        const allStorageKeys = Object.keys(localStorage)

        // 清理所有localStorage中的相关数据
        const chatKeys = allStorageKeys.filter(
          (key) =>
            key.startsWith('chat-') ||
            key.startsWith('conversation-') ||
            key.includes('cerebro-') ||
            key.includes('ai-') ||
            key.includes('message-') ||
            key.includes('session-') ||
            key.includes('user-message-')
        )

        console.log('[退出登录] 清除聊天相关数据:', chatKeys)
        chatKeys.forEach((key) => localStorage.removeItem(key))

        // 清理所有sessionStorage中的相关数据
        const sessionKeys = Object.keys(sessionStorage)
        const chatSessionKeys = sessionKeys.filter(
          (key) =>
            key.startsWith('chat') ||
            key.includes('message') ||
            key.includes('session') ||
            key.includes('thinking') ||
            key.includes('conversation')
        )

        console.log('[退出登录] 清除会话相关数据:', chatSessionKeys)
        chatSessionKeys.forEach((key) => sessionStorage.removeItem(key))

        console.log('[退出登录] 数据清理完成')

        return true
      } else {
        throw new Error(response.data.msg || '退出失败')
      }
    } catch (error) {
      console.error('退出失败:', error)
      return false
    }
  },

  // 获取用户信息
  getUserProfile: async (): Promise<UserProfile | null> => {
    try {
      const apiPrefix = config.env === 'production' ? '' : '/talent'
      const response = await request.get({ url: `${apiPrefix}/app-api/member/user/get` })

      if (response.data.code === 0) {
        return response.data.data
      } else {
        throw new Error(response.data.msg || '获取用户信息失败')
      }
    } catch (error) {
      console.error('获取用户信息失败:', error)
      return null
    }
  }
}
