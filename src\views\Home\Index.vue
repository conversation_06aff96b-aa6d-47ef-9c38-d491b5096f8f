<template>
  <div class="home-container">
    <!-- 两列布局 -->
    <div class="left-column">
      <!-- 左侧上方卡片 -->
      <div class="search-card">
        <div class="card-bg"></div>
        <div class="card-content">
          <div class="card-content-top">
            <h2>AI 找爆款</h2>
            <div class="card-desc"
              >通过关键词分析抖音、小红书等平台的爆款视频，发现热门内容趋势</div
            >
          </div>
          <!-- 修改搜索框和按钮部分 -->
          <div class="search-input">
            <el-input placeholder="输入关键词，如：美妆、穿搭、美食教程..." v-model="searchKeyword">
              <template #suffix>
                <el-button circle class="search-btn" @click="handleSearch">
                  <el-icon><Position /></el-icon>
                </el-button>
              </template>
            </el-input>
          </div>
        </div>
      </div>
      <div class="card-footer">
        <el-button type="primary" text @click="showAllPlatforms">
          <el-icon><Grid /></el-icon>
          全部平台
        </el-button>
      </div>
      <!-- 左侧下方瀑布流图片 -->
      <div class="waterfall-container">
        <h3>爆款视频分析结果</h3>
        <div class="waterfall">
          <div v-for="(item, index) in imageList" :key="index" class="waterfall-item">
            <el-image :src="item.url" fit="cover" lazy />
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧AI对话组件 -->
    <div class="right-column">
      <AiChatWidget
        title="AI设计师"
        welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
        :suggestions="aiSuggestions"
        placeholder="在这里输入问题"
        @message-sent="handleMessageSent"
        @message-received="handleMessageReceived"
        @error="handleChatError"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted } from 'vue'
import { Grid } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 搜索关键词
const searchKeyword = ref('')

// AI建议问题
const aiSuggestions = ref(['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计'])

// 模拟瀑布流图片数据
const imageList = ref([
  { url: '/src/assets/imgs/ai/dall2.jpg' },
  { url: '/src/assets/imgs/ai/dall3.jpg' },
  { url: '/src/assets/imgs/ai/qingxi.jpg' },
  { url: '/src/assets/imgs/ai/ziran.jpg' },
  { url: '/src/assets/imgs/profile.jpg' },
  { url: '/src/assets/imgs/avatar.jpg' }
])

// 方法
const handleSearch = () => {
  // 实际项目中这里会调用API获取数据
  console.log('搜索关键词:', searchKeyword.value)
}

const showAllPlatforms = () => {
  console.log('显示全部平台')
}

// AI聊天事件处理
const handleMessageSent = (message: string) => {
  console.log('消息已发送:', message)
}

const handleMessageReceived = (message: string) => {
  console.log('收到回复:', message)
}

const handleChatError = (error: Error) => {
  console.error('聊天错误:', error)
  ElMessage.error(`聊天出错: ${error.message}`)
}

onMounted(() => {
  // 页面加载完成后的初始化逻辑
})
</script>

<style lang="scss" scoped>
.home-container {
  display: flex;
  width: 100%;
  height: 100%;
  gap: 20px;
}

.left-column {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.right-column {
  width: 350px;
  border-radius: 8px;
}

// 左侧上方卡片
.search-card {
  position: relative;
  display: flex;
  height: 188px; // 调整高度
  overflow: hidden;
  color: #fff;
  border-radius: 16px; // 增加圆角
  box-sizing: border-box;
  flex-direction: column;

  .card-bg {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    background-image: url('@/assets/imgs/bg.jpg');
    background-position: center;
    background-size: cover;
  }

  .card-content {
    flex: 1;
    z-index: 2;

    h2 {
      margin-bottom: 8px;
      font-size: 22px;
      font-weight: 600; // 加粗标题
    }

    .card-desc {
      margin-bottom: 24px; // 增加与搜索框的距离
      font-size: 14px;
      font-weight: 300; // 字体细一点
      opacity: 0.85; // 提高可见度
    }

    .search-input {
      .el-input {
        overflow: hidden;
        border-radius: 24px; // 更圆润的边框

        :deep(.el-input__wrapper) {
          height: 100px; // 增加高度
          padding: 5px 5px 5px 16px; // 增加左侧内边距
          background-color: rgb(29 32 62 / 50%); // 深色系背景
          border: none;
          box-shadow: 0 0 0 1px rgb(255 255 255 / 15%) inset; // 细微边框
          backdrop-filter: blur(8px); // 增强模糊效果
        }

        :deep(.el-input__inner) {
          height: 42px;
          font-size: 14px;
          color: #fff;

          &::placeholder {
            font-weight: 300;
            color: rgb(255 255 255 / 70%);
          }
        }

        :deep(.el-input__suffix) {
          margin-right: 4px;
        }
      }

      .search-btn {
        width: 32px;
        height: 32px;
        color: #fff;
        background: linear-gradient(135deg, #4080ff, #5c9dff); // 渐变背景
        border: none;
        box-shadow: 0 2px 8px rgb(64 128 255 / 40%); // 添加阴影
      }
    }
  }

  .card-footer {
    z-index: 1;
    display: flex;
    justify-content: flex-start;
    margin-top: 8px; // 增加与搜索框的距离

    .el-button {
      padding: 6px 12px;
      font-weight: 300;

      .el-icon {
        margin-right: 4px;
      }
    }
  }
}

// 左侧下方瀑布流
.waterfall-container {
  h3 {
    margin-bottom: 16px;
    font-weight: 500;
  }

  .waterfall {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    grid-gap: 16px;

    .waterfall-item {
      margin-bottom: 16px;
      overflow: hidden;
      border-radius: 8px;
      break-inside: avoid;

      img {
        display: block;
        width: 100%;
      }
    }
  }
}

// 右侧AI对话
.right-column {
  width: 400px;
  height: 100%;
}
</style>
