<template>
  <div class="height-test-page">
    <ContentWrap title="聊天组件高度测试">
      <div class="test-info">
        <p>这个页面用于测试AI聊天组件的高度是否固定。</p>
        <p>预期行为：组件高度应该保持固定，消息多了应该在内部滚动，而不是让整个组件变高。</p>
      </div>
      
      <div class="test-containers">
        <!-- 固定高度测试 -->
        <div class="test-section">
          <h3>固定高度测试 (400px)</h3>
          <div class="container-400">
            <AiChatWidget 
              height="400px"
              title="高度测试"
              welcome-message="请发送一些消息来测试高度是否固定。组件高度应该保持在400px。"
              :suggestions="['发送长消息测试', '测试多条消息', '测试流式生成']"
              @message-sent="onMessageSent"
              @message-received="onMessageReceived"
            />
          </div>
          <div class="height-indicator">
            容器高度: 400px (红色边框)
          </div>
        </div>

        <!-- 百分比高度测试 -->
        <div class="test-section">
          <h3>百分比高度测试 (100%)</h3>
          <div class="container-percent">
            <AiChatWidget 
              height="100%"
              title="百分比高度测试"
              welcome-message="这个组件使用100%高度，应该填满父容器。"
              :suggestions="['测试百分比高度', '发送消息测试滚动']"
              @message-sent="onMessageSent"
              @message-received="onMessageReceived"
            />
          </div>
          <div class="height-indicator">
            容器高度: 300px (蓝色边框)
          </div>
        </div>
      </div>

      <!-- 测试日志 -->
      <div class="test-log">
        <h3>测试日志</h3>
        <div class="log-container">
          <div v-for="(log, index) in logs" :key="index" class="log-item">
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
        <el-button @click="clearLogs" size="small">清空日志</el-button>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

defineOptions({ name: 'HeightTest' })

const logs = ref<Array<{ time: string; message: string }>>([])

const addLog = (message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.unshift({ time, message })
  
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20)
  }
}

const onMessageSent = (message: string) => {
  addLog(`用户发送: ${message}`)
}

const onMessageReceived = (message: string) => {
  addLog(`AI回复: ${message.substring(0, 30)}...`)
}

const clearLogs = () => {
  logs.value = []
  addLog('日志已清空')
}

// 初始化
addLog('页面加载完成，开始高度测试')
</script>

<style lang="scss" scoped>
.height-test-page {
  .test-info {
    margin-bottom: 24px;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;

    p {
      margin: 0 0 8px 0;
      color: #0c4a6e;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .test-containers {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 24px;
    margin-bottom: 24px;
  }

  .test-section {
    h3 {
      margin: 0 0 12px 0;
      color: #1f2937;
      font-size: 16px;
      font-weight: 600;
    }

    .container-400 {
      height: 400px;
      border: 2px solid #ef4444;
      border-radius: 8px;
      padding: 8px;
      background: #fef2f2;
    }

    .container-percent {
      height: 300px;
      border: 2px solid #3b82f6;
      border-radius: 8px;
      padding: 8px;
      background: #eff6ff;
    }

    .height-indicator {
      margin-top: 8px;
      padding: 8px;
      background: #f3f4f6;
      border-radius: 4px;
      font-size: 12px;
      color: #6b7280;
      text-align: center;
    }
  }

  .test-log {
    h3 {
      margin: 0 0 12px 0;
      color: #1f2937;
      font-size: 16px;
      font-weight: 600;
    }

    .log-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background: #ffffff;
      margin-bottom: 12px;

      .log-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-bottom: 1px solid #f3f4f6;
        font-size: 13px;

        &:last-child {
          border-bottom: none;
        }

        .log-time {
          color: #6b7280;
          font-family: monospace;
          min-width: 80px;
        }

        .log-message {
          color: #4b5563;
          flex: 1;
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .height-test-page .test-containers {
    grid-template-columns: 1fr;
  }
}
</style>
