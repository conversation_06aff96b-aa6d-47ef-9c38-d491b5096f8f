<template>
  <div class="chat-widget-demo">
    <ContentWrap title="AI聊天组件演示">
      <div class="demo-container">
        <!-- 基础用法 -->
        <div class="demo-section">
          <h3>基础用法</h3>
          <p>最简单的使用方式，使用默认配置</p>
          <div class="demo-widget">
            <AiChatWidget 
              height="400px"
              @message-sent="handleMessageSent"
              @message-received="handleMessageReceived"
              @error="handleError"
            />
          </div>
        </div>

        <!-- 自定义配置 -->
        <div class="demo-section">
          <h3>自定义配置</h3>
          <p>自定义欢迎消息、建议问题和外观</p>
          <div class="demo-widget">
            <AiChatWidget 
              height="400px"
              title="设计助手"
              welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
              :suggestions="['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计']"
              placeholder="在这里输入设计需求..."
              @message-sent="handleMessageSent"
              @message-received="handleMessageReceived"
              @error="handleError"
            />
          </div>
        </div>

        <!-- 浅色主题 -->
        <div class="demo-section">
          <h3>浅色主题</h3>
          <p>使用浅色主题的聊天组件</p>
          <div class="demo-widget light-theme">
            <AiChatWidget 
              height="400px"
              theme="light"
              title="客服助手"
              welcome-message="您好！我是智能客服助手，有什么可以帮助您的吗？"
              :suggestions="['查询订单状态', '退换货政策', '联系人工客服']"
              @message-sent="handleMessageSent"
              @message-received="handleMessageReceived"
              @error="handleError"
            />
          </div>
        </div>

        <!-- 紧凑模式 -->
        <div class="demo-section">
          <h3>紧凑模式</h3>
          <p>适合嵌入到侧边栏或小窗口的紧凑模式</p>
          <div class="demo-widget compact">
            <AiChatWidget 
              height="300px"
              width="300px"
              title="AI助手"
              :show-header="false"
              welcome-message="有什么可以帮您的？"
              :suggestions="['快速问答', '智能推荐']"
              @message-sent="handleMessageSent"
              @message-received="handleMessageReceived"
              @error="handleError"
            />
          </div>
        </div>

        <!-- 自定义API Key -->
        <div class="demo-section">
          <h3>自定义API配置</h3>
          <p>使用不同的API Key和URL</p>
          <div class="demo-controls">
            <el-form :model="apiConfig" label-width="100px" size="small">
              <el-form-item label="API Key">
                <el-input 
                  v-model="apiConfig.apiKey" 
                  placeholder="输入自定义API Key"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item label="API URL">
                <el-input 
                  v-model="apiConfig.apiUrl" 
                  placeholder="输入自定义API URL"
                />
              </el-form-item>
              <el-form-item label="系统提示">
                <el-input 
                  v-model="apiConfig.systemPrompt" 
                  type="textarea"
                  placeholder="输入系统提示词"
                  :rows="2"
                />
              </el-form-item>
            </el-form>
          </div>
          <div class="demo-widget">
            <AiChatWidget 
              height="400px"
              :api-key="apiConfig.apiKey"
              :api-url="apiConfig.apiUrl"
              :system-prompt="apiConfig.systemPrompt"
              title="自定义配置"
              @message-sent="handleMessageSent"
              @message-received="handleMessageReceived"
              @error="handleError"
            />
          </div>
        </div>

        <!-- 事件监听 -->
        <div class="demo-section">
          <h3>事件监听</h3>
          <p>监听组件的各种事件</p>
          <div class="event-log">
            <h4>事件日志：</h4>
            <div class="log-container">
              <div 
                v-for="(log, index) in eventLogs" 
                :key="index"
                class="log-item"
                :class="log.type"
              >
                <span class="log-time">{{ log.time }}</span>
                <span class="log-event">{{ log.event }}</span>
                <span class="log-data">{{ log.data }}</span>
              </div>
            </div>
            <el-button @click="clearLogs" size="small" type="danger">清空日志</el-button>
          </div>
        </div>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'AiChatWidgetDemo' })

// API配置
const apiConfig = reactive({
  apiKey: '',
  apiUrl: '',
  systemPrompt: '你是一个友好的AI助手，请用简洁明了的语言回答用户的问题。'
})

// 事件日志
const eventLogs = ref<Array<{
  time: string
  event: string
  data: string
  type: string
}>>([])

// 添加日志
const addLog = (event: string, data: any, type: string = 'info') => {
  const time = new Date().toLocaleTimeString()
  eventLogs.value.unshift({
    time,
    event,
    data: typeof data === 'string' ? data : JSON.stringify(data),
    type
  })
  
  // 限制日志数量
  if (eventLogs.value.length > 50) {
    eventLogs.value = eventLogs.value.slice(0, 50)
  }
}

// 事件处理函数
const handleMessageSent = (message: string) => {
  addLog('message-sent', message, 'success')
  ElMessage.success(`消息已发送: ${message.substring(0, 20)}...`)
}

const handleMessageReceived = (message: string) => {
  addLog('message-received', message, 'info')
  ElMessage.info(`收到回复: ${message.substring(0, 20)}...`)
}

const handleError = (error: Error) => {
  addLog('error', error.message, 'error')
  ElMessage.error(`发生错误: ${error.message}`)
}

// 清空日志
const clearLogs = () => {
  eventLogs.value = []
}
</script>

<style lang="scss" scoped>
.chat-widget-demo {
  .demo-container {
    display: flex;
    flex-direction: column;
    gap: 32px;
  }

  .demo-section {
    h3 {
      margin: 0 0 8px 0;
      color: #1f2937;
      font-size: 18px;
      font-weight: 600;
    }

    p {
      margin: 0 0 16px 0;
      color: #6b7280;
      font-size: 14px;
    }

    .demo-widget {
      border: 1px solid #e5e7eb;
      border-radius: 8px;
      padding: 16px;
      background: #f9fafb;

      &.light-theme {
        background: #ffffff;
      }

      &.compact {
        display: inline-block;
        width: auto;
      }
    }

    .demo-controls {
      margin-bottom: 16px;
      padding: 16px;
      background: #f3f4f6;
      border-radius: 8px;
    }
  }

  .event-log {
    h4 {
      margin: 0 0 12px 0;
      color: #1f2937;
      font-size: 16px;
      font-weight: 600;
    }

    .log-container {
      max-height: 200px;
      overflow-y: auto;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background: #ffffff;
      margin-bottom: 12px;

      .log-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 8px 12px;
        border-bottom: 1px solid #f3f4f6;
        font-size: 12px;

        &:last-child {
          border-bottom: none;
        }

        &.success {
          background: #f0f9ff;
          border-left: 3px solid #0ea5e9;
        }

        &.info {
          background: #f0fdf4;
          border-left: 3px solid #22c55e;
        }

        &.error {
          background: #fef2f2;
          border-left: 3px solid #ef4444;
        }

        .log-time {
          color: #6b7280;
          font-family: monospace;
          min-width: 80px;
        }

        .log-event {
          color: #1f2937;
          font-weight: 600;
          min-width: 120px;
        }

        .log-data {
          color: #4b5563;
          flex: 1;
          word-break: break-all;
        }
      }
    }
  }
}
</style>
