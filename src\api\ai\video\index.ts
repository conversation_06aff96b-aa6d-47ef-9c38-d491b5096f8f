import request from '@/config/axios'

// AI 视频生成 VO
export interface VideoVO {
  id: number // 编号
  platform: string // 平台
  model: string // 模型
  prompt: string // 提示词
  width: number // 视频宽度
  height: number // 视频高度
  duration: number // 视频时长（秒）
  status: number // 状态
  publicStatus: boolean // 公开状态
  videoUrl: string // 视频地址
  errorMessage: string // 错误信息
  options: any // 配置 Map<string, string>
  taskId: number // 任务编号
  createTime: Date // 创建时间
  finishTime: Date // 完成时间
}

export interface VideoGenerateReqVO {
  prompt: string // 提示词
  modelId: number // 模型
  aspectRatio: string // 视频比例 (16:9, 9:16)
  duration: string // 视频时长 (5S, 10S)
  style: string // 视频生成的风格
  options: object // 生成参数，Map<String, String>
}

// Dify 视频生成请求 VO
export interface DifyVideoGenerateReqVO {
  inputs: {
    input: string // 提示词
    ratio: string // 视频比例
    duration: string // 视频时长
  }
  query: string // 用户查询
  user: string // 用户标识
  response_mode: string // 响应模式
  tool_parameters?: {
    video_generation_ratio?: string // 视频生成比例
    video_generation_duration?: string // 视频生成时长
    video_generation_n?: number // 生成数量
  }
}

// Dify 视频生成响应 VO
export interface DifyVideoGenerateRespVO {
  data: {
    outputs: {
      video_url: string // 生成的视频URL
    }
  }
  answer?: string // 错误信息或其他回复
}

// AI 视频 API
export const VideoApi = {
  // 获取【我的】视频分页
  getVideoPageMy: async (params: any) => {
    return await request.get({ url: `/ai/video/my-page`, params })
  },

  // 获取【我的】视频记录
  getVideoMy: async (id: number) => {
    return await request.get({ url: `/ai/video/get-my?id=${id}` })
  },

  // 获取【我的】视频记录列表
  getVideoListMyByIds: async (ids: number[]) => {
    return await request.get({ url: `/ai/video/my-list-by-ids`, params: { ids: ids.join(',') } })
  },

  // 生成视频
  generateVideo: async (data: VideoGenerateReqVO) => {
    return await request.post({ url: `/ai/video/generate`, data })
  },

  // 删除【我的】视频记录
  deleteVideoMy: async (id: number) => {
    return await request.delete({ url: `/ai/video/delete-my?id=${id}` })
  },

  // ================ Dify 专属 ================

  // 【Dify】生成视频
  difyGenerateVideo: async (data: DifyVideoGenerateReqVO) => {
    const apiUrl = import.meta.env.VITE_DIFY_API_URL + '/workflows/run'
    const apiKey = import.meta.env.VITE_DIFY_VIDEO_API_KEY

    if (!apiKey) {
      throw new Error('未配置 Dify API Key')
    }

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${apiKey}`
      },
      body: JSON.stringify(data)
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.message || 'API 请求失败')
    }

    return (await response.json()) as DifyVideoGenerateRespVO
  },

  // ================ 视频管理 ================

  // 查询视频分页
  getVideoPage: async (params: any) => {
    return await request.get({ url: `/ai/video/page`, params })
  },

  // 更新视频发布状态
  updateVideo: async (data: any) => {
    return await request.put({ url: '/ai/video/update', data })
  },

  // 删除视频
  deleteVideo: async (id: number) => {
    return await request.delete({ url: `/ai/video/delete?id=` + id })
  }
}
