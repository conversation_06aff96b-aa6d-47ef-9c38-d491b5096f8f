<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 14 14"><g clip-path="url(#a)"><path fill="url(#b)" d="M4.944 5.142c-.096-.293-.144-.44-.224-.467a.168.168 0 0 0-.107 0c-.08.027-.128.174-.224.467-.404 1.227-.605 1.84-.986 2.326a3.525 3.525 0 0 1-.601.602c-.486.38-1.1.582-2.327.986-.293.096-.44.144-.466.224a.169.169 0 0 0 0 .107c.026.08.173.128.466.224 1.227.404 1.84.605 2.327.986.224.176.425.377.6.601.382.486.583 1.1.987 2.327.096.293.144.44.224.466a.168.168 0 0 0 .107 0c.08-.026.128-.173.224-.466.404-1.227.606-1.84.986-2.327.176-.224.378-.425.602-.6.486-.382 1.1-.583 2.326-.987.293-.096.44-.145.467-.224a.168.168 0 0 0 0-.107c-.027-.08-.174-.128-.467-.224-1.227-.404-1.84-.606-2.326-.986a3.525 3.525 0 0 1-.602-.602c-.38-.486-.582-1.1-.986-2.326Zm6.13-2.492c-.064-.195-.096-.293-.15-.31a.112.112 0 0 0-.07 0c-.054.017-.086.115-.15.31-.27.818-.404 1.227-.658 1.551a2.35 2.35 0 0 1-.4.4c-.324.255-.734.39-1.552.658-.195.065-.293.097-.31.15a.112.112 0 0 0 0 .071c.017.053.115.085.31.15.818.269 1.228.403 1.552.657.149.117.283.252.4.4.254.325.389.734.658 1.552.064.195.096.293.15.31a.112.112 0 0 0 .07 0c.054-.017.086-.115.15-.31.27-.818.404-1.227.658-1.551.116-.15.251-.284.4-.401.324-.254.733-.388 1.551-.657.196-.065.293-.097.311-.15a.113.113 0 0 0 0-.071c-.018-.053-.115-.085-.31-.15-.819-.269-1.228-.403-1.552-.657a2.351 2.351 0 0 1-.4-.4c-.254-.325-.389-.734-.658-1.552ZM6.727.198c-.04-.122-.06-.183-.094-.194a.07.07 0 0 0-.044 0c-.033.01-.054.072-.094.194-.168.511-.252.767-.41.97a1.472 1.472 0 0 1-.251.25c-.203.159-.458.243-.97.41-.122.04-.183.061-.194.094a.07.07 0 0 0 0 .045c.011.033.072.053.195.093.51.168.766.252.969.411.093.073.177.157.25.25.16.203.243.459.411.97.04.122.06.183.094.194a.07.07 0 0 0 .044 0c.034-.01.054-.072.094-.194.168-.511.252-.767.41-.97a1.47 1.47 0 0 1 .251-.25c.203-.159.458-.243.97-.41.122-.041.183-.061.194-.094a.07.07 0 0 0 0-.045c-.011-.033-.072-.053-.194-.093-.512-.168-.767-.252-.97-.411a1.47 1.47 0 0 1-.25-.25c-.159-.203-.243-.459-.411-.97Z"/></g><defs><linearGradient gradientUnits="userSpaceOnUse" y2="14" x2="7" y1="0" x1="7" id="b"><stop stop-color="#675DFF"/><stop stop-color="#BF78E6" offset="1"/></linearGradient><clipPath id="a"><path fill="#fff" d="M0 0h14v14H0z" data-follow-fill="#fff"/></clipPath></defs></svg>