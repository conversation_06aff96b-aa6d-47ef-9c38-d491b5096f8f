import AiChatWidget from './index.vue'
import type { 
  AiChatWidgetProps, 
  AiChatWidgetEvents, 
  AiChatWidgetMethods,
  ChatMessage,
  ChatOptions,
  StreamMessageCallback,
  StreamErrorCallback,
  StreamCompleteCallback
} from './types'

// 导出组件
export default AiChatWidget

// 导出类型
export type {
  AiChatWidgetProps,
  AiChatWidgetEvents,
  AiChatWidgetMethods,
  ChatMessage,
  ChatOptions,
  StreamMessageCallback,
  StreamErrorCallback,
  StreamCompleteCallback
}

// 组件安装函数（用于全局注册）
export const install = (app: any) => {
  app.component('AiChatWidget', AiChatWidget)
}
