/**
 * AI聊天组件相关类型定义
 */

// 消息类型
export interface ChatMessage {
  id: string
  type: 'user' | 'ai'
  content: string
  timestamp: number
  isStreaming?: boolean
}

// 组件Props接口
export interface AiChatWidgetProps {
  // API配置
  apiKey?: string
  apiUrl?: string
  
  // 外观配置
  height?: string
  width?: string
  theme?: 'dark' | 'light'
  
  // 功能配置
  placeholder?: string
  maxMessages?: number
  enableHistory?: boolean
  enableClear?: boolean
  showHeader?: boolean
  title?: string
  
  // 初始配置
  welcomeMessage?: string
  suggestions?: string[]
  
  // 高级配置
  user?: string
  systemPrompt?: string
}

// 组件Events接口
export interface AiChatWidgetEvents {
  'message-sent': [message: string]
  'message-received': [message: string]
  'error': [error: Error]
  'stream-start': []
  'stream-end': []
}

// 组件实例方法接口
export interface AiChatWidgetMethods {
  // 发送消息
  sendMessage: (message?: string) => Promise<void>
  // 清空消息
  clearMessages: () => void
  // 滚动到底部
  scrollToBottom: () => void
  // 获取消息列表
  getMessages: () => ChatMessage[]
  // 设置消息列表
  setMessages: (messages: ChatMessage[]) => void
}

// 聊天配置选项
export interface ChatOptions {
  user?: string
  conversationId?: string
  systemPrompt?: string
}

// 流式聊天回调函数类型
export type StreamMessageCallback = (content: string, isComplete: boolean) => void
export type StreamErrorCallback = (error: Error) => void
export type StreamCompleteCallback = () => void
