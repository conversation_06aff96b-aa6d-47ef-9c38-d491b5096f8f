<template>
  <div class="simple-height-test">
    <ContentWrap title="简单高度测试">
      <div class="test-description">
        <p><strong>测试目标：</strong>验证AI聊天组件的高度是否能保持固定</p>
        <p><strong>测试方法：</strong>发送消息，观察红色边框容器是否保持400px高度</p>
        <p><strong>预期结果：</strong>无论发送多少消息，红色容器高度都应该保持400px不变</p>
      </div>

      <div class="test-container">
        <h3>固定高度测试 (400px)</h3>
        <div class="height-container">
          <AiChatWidget 
            height="400px"
            width="100%"
            title="高度测试"
            welcome-message="请发送消息测试高度是否固定在400px"
            :suggestions="['发送第一条消息', '发送第二条消息', '发送第三条消息']"
            placeholder="输入测试消息..."
          />
        </div>
        <div class="container-info">
          <p>红色边框容器高度: 400px</p>
          <p>如果组件高度正确，它应该完全填满红色容器，不多不少</p>
        </div>
      </div>

      <div class="test-container">
        <h3>百分比高度测试 (100%)</h3>
        <div class="percent-container">
          <AiChatWidget 
            height="100%"
            width="100%"
            title="百分比测试"
            welcome-message="这个组件使用100%高度，应该填满蓝色容器"
            :suggestions="['测试百分比高度', '发送更多消息']"
            placeholder="输入测试消息..."
          />
        </div>
        <div class="container-info">
          <p>蓝色边框容器高度: 300px</p>
          <p>组件应该完全填满蓝色容器</p>
        </div>
      </div>

      <div class="debug-info">
        <h3>调试信息</h3>
        <p>如果高度仍然有问题，请检查：</p>
        <ul>
          <li>组件是否有正确的 min-height 和 max-height</li>
          <li>消息容器是否有正确的 overflow 设置</li>
          <li>flex 布局是否正确配置</li>
        </ul>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
defineOptions({ name: 'SimpleHeightTest' })
</script>

<style lang="scss" scoped>
.simple-height-test {
  .test-description {
    margin-bottom: 24px;
    padding: 16px;
    background: #f0f9ff;
    border: 1px solid #0ea5e9;
    border-radius: 8px;

    p {
      margin: 0 0 8px 0;
      color: #0c4a6e;

      &:last-child {
        margin-bottom: 0;
      }

      strong {
        font-weight: 600;
      }
    }
  }

  .test-container {
    margin-bottom: 32px;

    h3 {
      margin: 0 0 16px 0;
      color: #1f2937;
      font-size: 18px;
      font-weight: 600;
    }

    .height-container {
      height: 400px;
      border: 3px solid #ef4444;
      border-radius: 8px;
      background: #fef2f2;
      margin-bottom: 12px;
      
      // 确保容器不会被内容撑开
      overflow: hidden;
      box-sizing: border-box;
    }

    .percent-container {
      height: 300px;
      border: 3px solid #3b82f6;
      border-radius: 8px;
      background: #eff6ff;
      margin-bottom: 12px;
      
      // 确保容器不会被内容撑开
      overflow: hidden;
      box-sizing: border-box;
    }

    .container-info {
      padding: 12px;
      background: #f3f4f6;
      border-radius: 6px;

      p {
        margin: 0 0 4px 0;
        font-size: 14px;
        color: #4b5563;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }
  }

  .debug-info {
    margin-top: 32px;
    padding: 16px;
    background: #fef3c7;
    border: 1px solid #f59e0b;
    border-radius: 8px;

    h3 {
      margin: 0 0 12px 0;
      color: #92400e;
      font-size: 16px;
      font-weight: 600;
    }

    p {
      margin: 0 0 8px 0;
      color: #92400e;
    }

    ul {
      margin: 0;
      padding-left: 20px;
      color: #92400e;

      li {
        margin-bottom: 4px;
      }
    }
  }
}
</style>
