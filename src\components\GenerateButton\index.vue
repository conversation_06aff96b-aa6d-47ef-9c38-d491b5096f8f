<template>
  <button
    @click="handleClick"
    :disabled="disabled"
    class="generate-btn"
    :class="{ 'is-loading': loading }"
  >
    <!-- 正常状态 -->
    <span v-if="!loading" class="btn-content">
      <span class="btn-text">{{ text }}</span>
      <span v-if="cost !== null" class="btn-cost">+ {{ cost }}</span>
    </span>
    
    <!-- 加载状态 -->
    <span v-else class="btn-content loading">
      <div class="loading-spinner"></div>
      <span class="btn-text">{{ loadingText }}</span>
    </span>
  </button>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 按钮文本
  text: {
    type: String,
    default: '立即生成'
  },
  // 消耗值
  cost: {
    type: [Number, String],
    default: null
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  },
  // 加载中的文本
  loadingText: {
    type: String,
    default: '生成中...'
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['click'])

const handleClick = () => {
  if (!props.disabled && !props.loading) {
    emit('click')
  }
}
</script>

<style scoped>
.generate-btn {
  position: relative;
  width: 100%;
  padding: 16px 24px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
  color: white;
  cursor: pointer;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgb(108 92 231 / 30%);
  transition: all 0.3s ease;
}

.generate-btn::before {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 20%), transparent);
  content: '';
  transition: left 0.5s;
}

.generate-btn:hover::before {
  left: 100%;
}

.generate-btn:hover:not(:disabled):not(.is-loading) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgb(108 92 231 / 40%);
}

.generate-btn:active:not(:disabled):not(.is-loading) {
  transform: translateY(0);
}

.generate-btn:disabled,
.generate-btn.is-loading {
  cursor: not-allowed;
  background: linear-gradient(135deg, #555 0%, #444 100%);
  transform: none;
  box-shadow: none;
}

.generate-btn.is-loading {
  cursor: default;
  background: linear-gradient(135deg, #6c5ce7 0%, #a29bfe 100%);
  opacity: 0.8;
}

.btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.btn-content.loading {
  gap: 12px;
}

.btn-text {
  font-size: 16px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.btn-cost {
  font-size: 16px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
