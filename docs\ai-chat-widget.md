# AI聊天组件使用文档

## 概述

`AiChatWidget` 是一个通用的AI聊天组件，支持SSE流式生成，可以轻松集成到任何页面中。组件基于Dify API，提供了完整的聊天功能和美观的UI设计。

## 特性

- ✅ **SSE流式生成** - 支持实时流式文本生成
- ✅ **通用组件** - 可在多页面嵌入，具有良好的复用性
- ✅ **主题支持** - 支持深色和浅色两种主题
- ✅ **自定义配置** - 丰富的配置选项，满足不同场景需求
- ✅ **事件监听** - 完整的事件系统，方便业务集成
- ✅ **TypeScript支持** - 完整的类型定义

## 安装和注册

组件已经全局注册，可以直接在任何Vue组件中使用：

```vue
<template>
  <AiChatWidget />
</template>
```

## 基础用法

### 最简单的使用方式

```vue
<template>
  <AiChatWidget height="500px" />
</template>
```

### 自定义配置

```vue
<template>
  <AiChatWidget 
    height="500px"
    title="AI助手"
    welcome-message="您好！我是AI助手，有什么可以帮助您的吗？"
    :suggestions="['查询信息', '解答问题', '提供建议']"
    placeholder="请输入您的问题..."
    @message-sent="handleMessageSent"
    @message-received="handleMessageReceived"
    @error="handleError"
  />
</template>

<script setup>
const handleMessageSent = (message) => {
  console.log('用户发送:', message)
}

const handleMessageReceived = (message) => {
  console.log('AI回复:', message)
}

const handleError = (error) => {
  console.error('聊天错误:', error)
}
</script>
```

## Props 配置

| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| `apiKey` | `string` | - | Dify API Key，不传则使用环境变量 |
| `apiUrl` | `string` | - | Dify API URL，不传则使用环境变量 |
| `height` | `string` | `'500px'` | 组件高度 |
| `width` | `string` | `'100%'` | 组件宽度 |
| `theme` | `'dark' \| 'light'` | `'dark'` | 主题模式 |
| `placeholder` | `string` | `'在这里输入问题...'` | 输入框占位符 |
| `maxMessages` | `number` | `50` | 最大消息数量 |
| `enableHistory` | `boolean` | `true` | 是否启用历史记录 |
| `enableClear` | `boolean` | `true` | 是否显示清空按钮 |
| `showHeader` | `boolean` | `true` | 是否显示头部 |
| `title` | `string` | `'AI助手'` | 头部标题 |
| `welcomeMessage` | `string` | `'Hi，我是你的AI助手...'` | 欢迎消息 |
| `suggestions` | `string[]` | `['帮我写一段代码', ...]` | 建议问题列表 |
| `user` | `string` | `'vue-user-123'` | 用户标识 |
| `systemPrompt` | `string` | - | 系统提示词 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| `message-sent` | `(message: string)` | 用户发送消息时触发 |
| `message-received` | `(message: string)` | 收到AI回复时触发 |
| `error` | `(error: Error)` | 发生错误时触发 |
| `stream-start` | `()` | 开始流式生成时触发 |
| `stream-end` | `()` | 流式生成结束时触发 |

## 使用场景

### 1. 首页集成

```vue
<template>
  <div class="home-layout">
    <div class="content">
      <!-- 主要内容 -->
    </div>
    <div class="sidebar">
      <AiChatWidget 
        height="100%"
        title="AI设计师"
        welcome-message="Hi，我是你的AI设计师，让我们开始今天的创作吧！"
        :suggestions="['帮我提写营销文案', '科技公司品牌logo设计', '游戏图标设计']"
      />
    </div>
  </div>
</template>
```

### 2. 客服系统

```vue
<template>
  <AiChatWidget 
    theme="light"
    title="智能客服"
    welcome-message="您好！我是智能客服助手，有什么可以帮助您的吗？"
    :suggestions="['查询订单状态', '退换货政策', '联系人工客服']"
    system-prompt="你是一个专业的客服助手，请用友好、专业的语言回答用户的问题。"
  />
</template>
```

### 3. 弹窗模式

```vue
<template>
  <el-dialog v-model="showChat" title="AI助手" width="500px">
    <AiChatWidget 
      height="400px"
      :show-header="false"
      welcome-message="有什么可以帮您的？"
    />
  </el-dialog>
</template>
```

### 4. 自定义API配置

```vue
<template>
  <AiChatWidget 
    :api-key="customApiKey"
    :api-url="customApiUrl"
    :system-prompt="customPrompt"
    title="专业助手"
  />
</template>

<script setup>
const customApiKey = 'your-custom-api-key'
const customApiUrl = 'https://your-custom-api.com/v1'
const customPrompt = '你是一个专业的技术顾问，请提供准确的技术建议。'
</script>
```

## 样式自定义

组件支持通过CSS变量进行样式自定义：

```css
.ai-chat-widget {
  --chat-primary-color: #6366f1;
  --chat-bg-color: #1a1a2e;
  --chat-text-color: #ffffff;
  --chat-border-color: rgba(255, 255, 255, 0.1);
}
```

## 注意事项

1. **API配置**: 确保正确配置Dify API Key和URL
2. **网络环境**: 组件需要网络连接才能正常工作
3. **错误处理**: 建议监听`error`事件进行错误处理
4. **性能优化**: 大量消息时会自动限制消息数量
5. **主题适配**: 根据页面主题选择合适的组件主题

## 开发和调试

### 查看演示页面

访问 `/ai/chat-widget-demo` 查看完整的使用示例和功能演示。

### 事件监听调试

```vue
<template>
  <AiChatWidget 
    @message-sent="console.log('发送:', $event)"
    @message-received="console.log('接收:', $event)"
    @error="console.error('错误:', $event)"
    @stream-start="console.log('开始流式生成')"
    @stream-end="console.log('流式生成结束')"
  />
</template>
```

## 更新日志

### v1.0.0
- 初始版本发布
- 支持SSE流式聊天
- 支持深色/浅色主题
- 完整的事件系统
- TypeScript支持
